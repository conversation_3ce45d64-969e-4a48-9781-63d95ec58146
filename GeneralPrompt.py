GENERATE_AVAILABLE_ACTIONS_PROMPT = (
    f"Generate all possible actions based on the current state, the goal state and the domain description above. Your answer must satisfy all the following requirements: (1)Each sentences should only contain one action, and actions are separated by a period, such as \"ACTION1. ACTION2.\". (2)After your analysis, you should organize all available actions into a new paragraph as the end of your answer without any other content. (3)DON'T USE ANY MARKDOWN SETTINGS.\n"
)

SCORING_AVAILABLE_ACTIONS_PROMPT = (
    f"Based on the domain description, the goal state, the current state and this possible action, you are reqiured to score this action from 0-5. Your answer must satisfy all the following requirements: (1)Your score should be a float number from 0-5 with one digit. (2) Your scoring criteria should include this action's possibility of reaching the goal state, the price of taking this action, and the difficulty of performing this action. (3)After your analysis, you should organize your final scores into a new paragraph as the end of your answer without any other content. (4)DON'T USE ANY MARKDOWN SETTINGS.\n"
)

GENERATE_NEXT_STATE_PROMPT = (
    f"Based on the domain description and the current state, take the given action and generate the new state. After your analysis, you should return the new state in a new paragraph as the end of your answer without any other content. Remember: DON'T USE ANY MARKDOWN FORMATTING.\n"
)