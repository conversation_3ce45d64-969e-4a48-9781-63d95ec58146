from tree import Node, SearchTree
from GeneralPrompt import GENERATE_AVAILABLE_ACTIONS_PROMPT, SCORING_AVAILABLE_ACTIONS_PROMPT
from DomainPrompt import DOMAIN_DESCRIPTION_BLOCKSWORLD
from utils import RequestSender, ExtractResponse


test_example = {"problem_id": "goalrecognition-blocksworld-5-2-12878", "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "observations": "<PERSON> moves the white block from the olive block onto the table. <PERSON> moves the olive block from the table to the magenta block.", "query": "the turquoise block is not on the table and the olive block is not on the table", "label": 1}

available_actions = [['Move the magenta block from the table to the white block', 4.5], ['Move the magenta block from the table to the red block', 2.5]]

search_tree = SearchTree(root_h_cost=0.0, initial_state=test_example["state"], query=test_example["query"])


for i in range(0,5):
    g = 1
    search_tree.add_node(parent_id=search_tree.root_id, state=test_example["state"], action="test", g_cost=g, h_cost=0.0, available_actions=available_actions, query=test_example["query"])
    g += 1

nodes = []

node_num = search_tree.nodes

for node_id in node_num:
    nodes.append(node_num[node_id])







print(search_tree.nodes)
print("=====")

