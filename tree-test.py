from tree import Node, SearchTree
from GeneralPrompt import GENERATE_AVAILABLE_ACTIONS_PROMPT, SCORING_AVAILABLE_ACTIONS_PROMPT
from DomainPrompt import DOMAIN_DESCRIPTION_BLOCKSWORLD
from utils import RequestSender, ExtractResponse

#GR-TRAC
test_example = {"problem_id": "goalrecognition-blocksworld-5-2-12878", "state": "The white block is on top of the olive block. The turquoise block is on the table. The olive block is on the table. The red block is on top of the turquoise block. The white block is clear. The magenta block is clear. The magenta block is on the table. The red block is clear.", "observations": "<PERSON> moves the white block from the olive block onto the table. <PERSON> moves the olive block from the table to the magenta block.", "query": "the turquoise block is not on the table and the olive block is not on the table", "label": 1}

tree = Node(node_id=1, parent_id=None, state=test_example["state"], query=test_example["query"], g_cost=0.0, h_cost=0.0, action_taken=[], history_actions=[])

extracted_response= ['Move the magenta block from the table to the white block', 'Move the magenta block from the table to the red block', 'Move the white block from the olive block onto the table', 'Move the red block from the turquoise block onto the table', 'Move the white block from the olive block to the red block', 'Move the white block from the olive block to the magenta block', 'Move the red block from the turquoise block to the white block', 'Move the red block from the turquoise block to the magenta block']

available_actions = [['Move the magenta block from the table to the white block', 4.5], ['Move the magenta block from the table to the red block', 2.5]]

tree.available_actions=available_actions

print(tree.available_actions)









