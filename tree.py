from typing import List, Any, Optional, Dict
import random
import string
import utils
import json
import heapq
from GeneralPrompt import GENERATE_AVAILABLE_ACTIONS_PROMPT,SCORING_AVAILABLE_ACTIONS_PROMPT, GENERATE_NEXT_STATE_PROMPT
from DomainPrompt import DOMAIN_DESCRIPTION_BLOCKSWORLD

class Node:
    """
    Description
    -----------
    Node of the tree, each node represents a state.
    
    Parameters
    ----------
    node_id : the ID of the node, which is a unique identifier for the node.
    parent_id : the parent id of this node.
    children_id : children of this node, corresponding to available_actions.
    available_actions : 2D list, each element is a list of [available_action, score].
    state : the state of the tree node.
    action_taken : the last action taken from parent to this node.
    history_actions : the actions taken from root to this node.
    g_cost : the cost from root to this node.
    h_cost : the cost from this node to goal, estimated by LLM.
    status : whether this node is active or pruned.
    flag : whether this state is the goal state.
    pruned_reason : if pruned, the reason for pruning.
    """
    
    def __init__(self, node_id: int, parent_id: Optional[int] = None, state: str = "", query: str = "", g_cost: float = 0.0, h_cost: float = 0.0, **kwargs):
        
        self.node_id: int = node_id
        self.parent_id: Optional[int] = parent_id
        self.children_id: List[int] = []
        self.available_actions: Optional[List[List[Any]]] = kwargs.get("available_actions", [])
        self.state: str = state
        self.action_taken: Optional[List[str]] = kwargs.get("action_taken", [])
        self.history_actions: List[str] = kwargs.get("history_actions", [])
        self.query: str = query
        self.g_cost: float = g_cost
        self.h_cost: float = h_cost
        self.flag: bool = False #True means this state is the goal state
        self.status: bool = True  # True (1) = active, False (0) = pruned
        self.pruned_reason: Optional[str] = None # INFINITE_HEURISTIC_VALUE or DOMAIN_KNOWLEDGE_VIOLATION
        
        
    @property
    def f_cost(self) -> float:
        """count total cost f, f = g+h = actual cost + heuristic cost. """
        h_val = self.h_cost if self.h_cost is not None else 0.0
        return self.g_cost + h_val


    def __lt__(self, other: 'Node') -> bool:
        """compare nodes for priority queue (A* algorithm)."""
        return self.f_cost < other.f_cost
    


class SearchTree:
    """
    Search tree with nodes.
    
    Parameters
    ----------
    root_h_cost : the heuristic cost of the root node.
    initial_state : the initial state.
    query : the goal state.
    
    """
    
    def __init__(self, domain:str, root_h_cost: float, initial_state: str, query:str, **kwargs):
        #All nodes
        self.nodes: Dict[int, Node] = {} 
        self.next_node_id: int = 0
    
        #ROOT NODE
        root_node = Node(node_id=self._get_new_id(), parent_id=-1, g_cost=0.0, h_cost=root_h_cost, state=initial_state, query=query) 
        self.root_id: int = root_node.node_id
        self.nodes[root_node.node_id] = root_node
        root_h_cost = root_h_cost

        #Plan info
        self.domain = domain
        self.initial_state = initial_state
        self.query: str = query

        #LLM info
        self.model = kwargs.get("model", "gpt-5")


    def _get_new_id(self) -> int:
        """return a new node id"""
        new_id = self.next_node_id
        self.next_node_id += 1
        return new_id


    def add_node(self, parent_id: int, state:str, action: str, g_cost: float, h_cost: float, available_actions: List[List[Any]], query: str) -> Node:
        
        """
        Add a new node into the tree based on current node
        """
        
        if parent_id not in self.nodes:
            raise ValueError(f"Illegal Parent ID: {parent_id}")
            
        parent_node = self.get_node(parent_id)
        history = parent_node.history_actions + [action]
        
        #store parent's id
        new_node = Node(
            node_id=self._get_new_id(),
            parent_id=parent_id,
            action_taken=action,
            g_cost=g_cost,
            h_cost=h_cost,
            state=state,
            available_actions=available_actions,
            history_actions=history,
            query=query
        )
        
        #add the new node into the node list
        self.nodes[new_node.node_id] = new_node
        
        #store children's id
        parent_node.children_id.append(new_node.node_id)
        
        return new_node

    def get_node(self, node_id: int) -> Node:
        """
        Assess a node by node_id
        """
        if node_id not in self.nodes:
            raise ValueError(f"Node ID {node_id} does not exist.")
        return self.nodes[node_id]


    def prune_node(self, node_id: int, reason: str):
        """
        Prune a node and set its status to False
        """
        node_to_prune = self.get_node(node_id)
        node_to_prune.status = False
        node_to_prune.pruned_reason = reason
        print(f"Node [{node_id}] has been pruned. Code: {reason}.")

    def a_star_search(self) -> Optional[Node]:
        """
        A* search algorithm implementation.
        With h=0 for all nodes, this becomes Dijkstra's algorithm.
        g_cost is defined as the number of actions from root to current node.
        Returns the goal node if found, None otherwise.
        """

        # Priority queue for open nodes (nodes to be explored)
        open_queue = []
        heapq.heappush(open_queue, self.nodes[self.root_id])
        
        # Set to track visited nodes
        visited = set()
        
        while open_queue:
            current_node = heapq.heappop(open_queue)
            
            # Skip if already visited
            if current_node.node_id in visited:
                continue
                
            visited.add(current_node.node_id)
            
            # Check if goal is reached
            if current_node.flag:  # Goal state found
                return current_node
            
            # Expand current node by generating children
            self._expand_node(current_node)
            
            # Add children to open queue
            for child_id in current_node.children_id:
                child_node = self.get_node(child_id)
                
                # Skip pruned or visited nodes
                if not child_node.status or child_id in visited:
                    continue
                    
                heapq.heappush(open_queue, child_node)
        
        return None  # No goal found


    def _expand_node(self, node: Node):
        """
        Expand a node by generating its children based on available actions.
        This is where you would call LLM to generate actions and next states.
        """
        # For now, this is a placeholder - you'll need to implement:
        # 1. Generate available actions for current state
        # 2. For each action, generate next state
        # 3. Calculate h_cost for next state
        # 4. Add child node using add_node()
        
        # Example structure:
        # for action, score in node.available_actions:
        #     next_state = self._generate_next_state(node.state, action)
        #     h_cost = 0.0  # As per your requirement
        #     self.add_node(node.node_id, next_state, action, h_cost, [])
        pass
    

    def _generate_next_state(self, current_state: str, action: str, **kwargs) -> str:
        """
        Generate the next state based on the current state and action.
        This is where you would call LLM to generate the next state.
        """
        
        prompt = (
            f"[Domain Description]{DOMAIN_DESCRIPTION_BLOCKSWORLD}\n"
            f"Current State:{current_state}\n"
            f"Action:{action}\n"
            f"{GENERATE_NEXT_STATE_PROMPT}"
        )

        response = utils.RequestSender(prompt, model=self.model, **kwargs)
        next_state = utils.ExtractResponse(response, response_type='STATE')
        
        return next_state
